#!/usr/bin/env python3
"""
OpenRouter CLI Launcher
Simple launcher script for the OpenRouter CLI AI Agent
"""

import sys
import subprocess
import os

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import prompt_toolkit
        import rich
        import httpx
        import keyring
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("📦 Install dependencies with: pip install -r requirements.txt")
        return False

def main():
    """Main launcher function"""
    print("🚀 Starting OpenRouter CLI AI Agent...")
    
    if not check_dependencies():
        sys.exit(1)
    
    try:
        from openrouter_cli import OpenRouterCLI
        import asyncio
        
        cli = OpenRouterCLI()
        asyncio.run(cli.run())
        
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error starting CLI: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
