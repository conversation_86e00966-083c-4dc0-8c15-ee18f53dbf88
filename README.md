# OpenRouter CLI AI Agent

A sophisticated command-line interface for interacting with OpenRouter AI models, featuring a rich terminal UI, conversation history, and seamless model switching.

## Features

✨ **Rich Terminal UI**
- Beautiful welcome banner and styled panels
- Command suggestions with arrow-key navigation
- Real-time status indicators and response statistics
- Syntax highlighting and formatted output

🤖 **AI Model Management**
- Support for all OpenRouter AI models
- Easy model switching with `/switch <model>`
- Popular models highlighted and categorized
- Current model displayed in prompt

💬 **Conversation Features**
- Persistent conversation history
- Context-aware responses (maintains conversation flow)
- Response time and statistics tracking
- Conversation export and management

🔐 **Security & Configuration**
- Secure API key storage using system keyring
- Persistent user preferences and settings
- Automatic configuration backup
- Environment variable support

⚡ **Advanced CLI Features**
- Tab completion for commands
- Command history with search
- Keyboard shortcuts (Ctrl+C, Ctrl+D)
- Error handling with helpful messages

## Installation

1. **Clone or download the project:**
   ```bash
   git clone <repository-url>
   cd openrouter-cli
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Get your OpenRouter API key:**
   - Visit [OpenRouter](https://openrouter.ai/keys)
   - Create an account and generate an API key
   - Add credits to your account

## Quick Start

1. **Run the CLI:**
   ```bash
   python run.py
   ```

2. **Set your API key:**
   ```
   /login
   ```

3. **Start chatting:**
   ```
   What is Python?
   ```

## Commands

| Command | Description |
|---------|-------------|
| `/help`, `/h`, `/?` | Show available commands and usage information |
| `/status` | Show current configuration and status |
| `/models` | List all available AI models |
| `/switch <model>` | Switch to a different AI model |
| `/login` | Set or update your OpenRouter API key |
| `/logout` | Remove stored API key |
| `/history`, `/hist` | Show conversation history |
| `/clear` | Clear the screen |
| `/exit`, `/quit` | Exit the application |

## Usage Examples

### Basic Chat
```
You > What is machine learning?
🤖 deepseek-r1 > Machine learning is a subset of artificial intelligence...
```

### Switch Models
```
You > /models
[Shows available models table]

You > /switch gpt-4
✅ Switched to model: gpt-4

You (gpt-4) > Explain quantum computing
```

### View History
```
You > /history
[Shows conversation history table with timestamps and statistics]
```

## Configuration

The CLI stores configuration in `~/.openrouter_cli/`:
- `config.json` - User preferences and settings
- `history.json` - Conversation history (last 100 conversations)

### Environment Variables

You can also set your API key via environment variable:
```bash
export OPENROUTER_API_KEY="your-api-key-here"
python run.py
```

## Popular Models

The CLI highlights popular models including:
- **GPT Models**: gpt-4, gpt-3.5-turbo
- **Claude Models**: claude-3-opus, claude-3-sonnet
- **DeepSeek**: deepseek-r1, deepseek-coder
- **Llama**: llama-3.1-70b, llama-3.1-8b
- **Gemini**: gemini-pro, gemini-flash

## Keyboard Shortcuts

- **Tab**: Auto-complete commands
- **↑/↓**: Navigate command history
- **Ctrl+C**: Interrupt current operation
- **Ctrl+D**: Exit application
- **Enter**: Submit command/message

## Error Handling

The CLI provides helpful error messages for common issues:
- ❌ Invalid API key
- 💳 Insufficient credits
- 🚦 Rate limiting
- 🌐 Network issues
- ⏱️ Request timeouts

## Troubleshooting

### API Key Issues
```bash
# Check if API key is set
/status

# Reset API key
/logout
/login
```

### Model Not Found
```bash
# List available models
/models

# Use exact model ID from the list
/switch deepseek/deepseek-r1
```

### Connection Issues
- Check your internet connection
- Verify your OpenRouter account has credits
- Try switching to a different model

## Development

### Project Structure
```
openrouter-cli/
├── openrouter_cli.py    # Main CLI application
├── run.py               # Launcher script
├── requirements.txt     # Dependencies
└── README.md           # Documentation
```

### Dependencies
- `prompt-toolkit` - Advanced terminal input handling
- `rich` - Rich text and beautiful formatting
- `httpx` - Modern HTTP client
- `keyring` - Secure credential storage
- `python-dotenv` - Environment variable loading

## License

MIT License - see LICENSE file for details.

## Contributing

Contributions welcome! Please feel free to submit issues and pull requests.

---

**Happy chatting with AI! 🤖✨**
