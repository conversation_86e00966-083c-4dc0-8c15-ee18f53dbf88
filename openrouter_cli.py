#!/usr/bin/env python3
"""
OpenRouter CLI AI Agent
A sophisticated command-line interface for interacting with OpenRouter AI models.
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

import httpx
from prompt_toolkit import PromptSession
from prompt_toolkit.completion import WordCompleter, Completer, Completion
from prompt_toolkit.formatted_text import HTML
from prompt_toolkit.key_binding import KeyBindings
from prompt_toolkit.shortcuts import confirm
from prompt_toolkit.application import Application
from prompt_toolkit.layout import Layout, HSplit, VSplit
from prompt_toolkit.widgets import Frame, Box
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from rich.layout import Layout
from rich.live import Live
from rich import box
import keyring
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class SmartCompleter(Completer):
    """Smart completer with command descriptions"""

    def __init__(self, commands: Dict[str, str]):
        self.commands = commands

    def get_completions(self, document, complete_event):
        text = document.text_before_cursor

        # Only complete if we're at the start or after whitespace and starting with /
        if text.startswith('/') or (text and text[-1].isspace() and '/' in text.split()[-1]):
            word = text.split()[-1] if text.split() else text

            for cmd, desc in self.commands.items():
                if cmd.startswith(word):
                    yield Completion(
                        cmd[len(word):],
                        display=f"{cmd} - {desc}",
                        style="class:completion-menu"
                    )

class OpenRouterClient:
    """Client for interacting with OpenRouter API"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv("OPENROUTER_API_KEY")
        self.base_url = "https://openrouter.ai/api/v1"
        self.current_model = "deepseek/deepseek-r1"
        self.conversation_history = []
        
    async def get_models(self) -> List[Dict]:
        """Fetch available models from OpenRouter"""
        if not self.api_key:
            return []

        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                response = await client.get(
                    f"{self.base_url}/models",
                    headers={
                        "Authorization": f"Bearer {self.api_key}",
                        "HTTP-Referer": "https://github.com/openrouter-cli",
                        "X-Title": "OpenRouter CLI"
                    }
                )
                response.raise_for_status()
                data = response.json().get("data", [])

                # Sort models by popularity/name
                return sorted(data, key=lambda x: (
                    x.get("top_provider", {}).get("max_completion_tokens", 0),
                    x.get("name", x.get("id", ""))
                ), reverse=True)

            except httpx.TimeoutException:
                return []
            except httpx.HTTPStatusError as e:
                if e.response.status_code == 401:
                    return []  # Invalid API key
                return []
            except Exception:
                return []
    
    async def chat_completion(self, messages: List[Dict], model: str = None) -> str:
        """Send chat completion request"""
        if not self.api_key:
            return "❌ Error: No API key configured. Use /login to set your API key."

        model = model or self.current_model

        async with httpx.AsyncClient(timeout=120.0) as client:
            try:
                # Prepare the request
                payload = {
                    "model": model,
                    "messages": messages,
                    "stream": False,
                    "max_tokens": 4000,
                    "temperature": 0.7
                }

                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers={
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json",
                        "HTTP-Referer": "https://github.com/openrouter-cli",
                        "X-Title": "OpenRouter CLI"
                    },
                    json=payload
                )

                response.raise_for_status()
                result = response.json()

                if "choices" in result and len(result["choices"]) > 0:
                    return result["choices"][0]["message"]["content"]
                else:
                    return "❌ Error: No response from AI model"

            except httpx.TimeoutException:
                return "⏱️ Error: Request timed out. The model might be busy."
            except httpx.HTTPStatusError as e:
                if e.response.status_code == 401:
                    return "🔑 Error: Invalid API key. Please check your credentials."
                elif e.response.status_code == 402:
                    return "💳 Error: Insufficient credits. Please check your OpenRouter account."
                elif e.response.status_code == 429:
                    return "🚦 Error: Rate limit exceeded. Please wait a moment and try again."
                else:
                    return f"🌐 Error: HTTP {e.response.status_code} - {e.response.text}"
            except Exception as e:
                return f"❌ Error: {str(e)}"

class ConfigManager:
    """Manages configuration and API keys"""
    
    def __init__(self):
        self.config_dir = Path.home() / ".openrouter_cli"
        self.config_file = self.config_dir / "config.json"
        self.config_dir.mkdir(exist_ok=True)
        self.config = self.load_config()
    
    def load_config(self) -> Dict:
        """Load configuration from file"""
        default_config = {
            "current_model": "deepseek/deepseek-r1",
            "history_limit": 100,
            "context_messages": 2,
            "auto_save": True,
            "theme": "default",
            "show_timestamps": True,
            "show_response_stats": True
        }

        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    loaded_config = json.load(f)
                    # Merge with defaults to ensure all keys exist
                    default_config.update(loaded_config)
                    return default_config
            except Exception:
                pass
        return default_config
    
    def save_config(self):
        """Save configuration to file"""
        with open(self.config_file, 'w') as f:
            json.dump(self.config, f, indent=2)
    
    def get_api_key(self) -> Optional[str]:
        """Get API key from keyring or environment"""
        try:
            key = keyring.get_password("openrouter_cli", "api_key")
            return key or os.getenv("OPENROUTER_API_KEY")
        except:
            return os.getenv("OPENROUTER_API_KEY")
    
    def set_api_key(self, api_key: str):
        """Store API key in keyring"""
        try:
            keyring.set_password("openrouter_cli", "api_key", api_key)
            return True
        except:
            return False

class HistoryManager:
    """Manages conversation history"""
    
    def __init__(self, config_dir: Path):
        self.history_file = config_dir / "history.json"
        self.history = self.load_history()
    
    def load_history(self) -> List[Dict]:
        """Load conversation history"""
        if self.history_file.exists():
            try:
                with open(self.history_file, 'r') as f:
                    return json.load(f)
            except:
                pass
        return []
    
    def save_history(self):
        """Save conversation history"""
        with open(self.history_file, 'w') as f:
            json.dump(self.history[-100:], f, indent=2)  # Keep last 100 entries
    
    def add_entry(self, user_input: str, ai_response: str, model: str):
        """Add new conversation entry"""
        entry = {
            "timestamp": datetime.now().isoformat(),
            "user": user_input,
            "assistant": ai_response,
            "model": model
        }
        self.history.append(entry)
        self.save_history()

class OpenRouterCLI:
    """Main CLI application class"""

    def __init__(self):
        self.console = Console()
        self.config_manager = ConfigManager()
        self.history_manager = HistoryManager(self.config_manager.config_dir)
        self.client = OpenRouterClient(self.config_manager.get_api_key())
        self.session = None
        self.running = True
        self.available_models = []

        # Command completions
        self.commands = {
            "/help": "Show available commands and usage information",
            "/h": "Show available commands and usage information (alias for /help)",
            "/?": "Show available commands and usage information (alias for /help)",
            "/status": "Show current configuration and status",
            "/models": "List all available AI models",
            "/switch": "Switch to a different AI model",
            "/login": "Set or update your OpenRouter API key",
            "/logout": "Remove stored API key",
            "/history": "Show conversation history",
            "/hist": "Show conversation history (alias for /history)",
            "/clear": "Clear the screen",
            "/exit": "Exit the application",
            "/quit": "Exit the application"
        }
        self.completer = SmartCompleter(self.commands)

        # Key bindings
        self.bindings = KeyBindings()
        self.setup_key_bindings()

    def setup_key_bindings(self):
        """Setup custom key bindings"""
        @self.bindings.add('c-c')
        def _(event):
            """Handle Ctrl+C"""
            event.app.exit(exception=KeyboardInterrupt)

        @self.bindings.add('c-d')
        def _(event):
            """Handle Ctrl+D"""
            event.app.exit()

    def create_welcome_panel(self) -> Panel:
        """Create the welcome banner panel"""
        welcome_text = Text()
        welcome_text.append("✨ Welcome to OpenRouter CLI!\n\n", style="bold cyan")
        welcome_text.append("/help", style="bold yellow")
        welcome_text.append(" for help, ", style="white")
        welcome_text.append("/status", style="bold yellow")
        welcome_text.append(" for your current setup\n\n", style="white")

        # Add current working directory
        welcome_text.append(f"cwd: {os.getcwd()}\n", style="dim")

        # Add quick start info
        welcome_text.append("\n💡 Quick Start:\n", style="bold blue")
        welcome_text.append("• Type ", style="white")
        welcome_text.append("/", style="bold yellow")
        welcome_text.append(" and press Tab for command suggestions\n", style="white")
        welcome_text.append("• Ask questions directly: ", style="white")
        welcome_text.append('"What is Python?"', style="italic green")
        welcome_text.append("\n• Use arrow keys to navigate command history", style="white")

        return Panel(
            welcome_text,
            title="🚀 OpenRouter CLI v1.0",
            subtitle="Powered by OpenRouter AI",
            border_style="cyan",
            box=box.DOUBLE
        )

    def create_tips_panel(self) -> Panel:
        """Create tips panel"""
        tips = Text()
        tips.append("Tips for getting started:\n\n", style="bold blue")
        tips.append("1. Run ", style="white")
        tips.append("/login", style="bold yellow")
        tips.append(" to set your OpenRouter API key\n", style="white")
        tips.append("2. Run ", style="white")
        tips.append("/models", style="bold yellow")
        tips.append(" to see available models\n", style="white")
        tips.append("3. Use ", style="white")
        tips.append("/switch <model>", style="bold yellow")
        tips.append(" to change AI model\n", style="white")
        tips.append("4. Type your questions directly or use commands\n", style="white")

        return Panel(tips, title="Getting Started", border_style="blue")

    def create_status_panel(self) -> Panel:
        """Create status panel"""
        status_table = Table.grid(padding=(0, 2))
        status_table.add_column(style="bold")
        status_table.add_column()

        # API Key status
        api_key = self.config_manager.get_api_key()
        if api_key:
            key_display = f"{api_key[:8]}..." if len(api_key) > 8 else api_key
            status_table.add_row("🔑 API Key:", f"[green]{key_display}[/green]")
        else:
            status_table.add_row("❌ API Key:", "[red]Not configured[/red] • Run [bold yellow]/login[/bold yellow]")

        # Current model
        status_table.add_row("🤖 Model:", f"[bold blue]{self.client.current_model}[/bold blue]")

        # History count
        history_count = len(self.history_manager.history)
        status_table.add_row("📚 History:", f"[dim]{history_count} conversations[/dim]")

        # Connection status
        connection_status = "🟢 Ready" if api_key else "🔴 Not Ready"
        status_table.add_row("🌐 Status:", connection_status)

        return Panel(
            status_table,
            title="📊 System Status",
            border_style="green",
            box=box.ROUNDED
        )

    async def process_command(self, command: str) -> bool:
        """Process slash commands. Returns True if command was handled."""
        original_command = command.strip()
        command = command.strip().lower()

        if command in ["/help", "/h", "/?"]:
            await self.show_help()
            return True
        elif command == "/status":
            self.console.print(self.create_status_panel())
            return True
        elif command == "/models":
            await self.show_models()
            return True
        elif command.startswith("/switch"):
            # Handle both "/switch model" and "/switch <model>"
            parts = original_command.split(None, 1)
            if len(parts) > 1:
                model = parts[1].strip()
                await self.switch_model(model)
            else:
                self.console.print("❌ Usage: /switch <model_id>", style="red")
                self.console.print("💡 Use /models to see available models", style="dim")
            return True
        elif command == "/login":
            await self.login()
            return True
        elif command == "/logout":
            await self.logout()
            return True
        elif command in ["/history", "/hist"]:
            await self.show_history()
            return True
        elif command == "/clear":
            self.console.clear()
            self.console.print(self.create_welcome_panel())
            return True
        elif command in ["/exit", "/quit"]:
            self.running = False
            return True
        else:
            # Unknown command
            self.console.print(f"❌ Unknown command: {original_command}", style="red")
            self.console.print("💡 Type /help to see available commands", style="dim")
            return True

        return False

    async def show_help(self):
        """Show help information"""
        help_table = Table(title="Available Commands", box=box.ROUNDED)
        help_table.add_column("Command", style="cyan", no_wrap=True)
        help_table.add_column("Description", style="white")

        commands_help = [
            ("/help, /h, /?", "Show available commands and usage information"),
            ("/status", "Show current configuration and status"),
            ("/models", "List all available AI models"),
            ("/switch <model>", "Switch to a different AI model"),
            ("/login", "Set or update your OpenRouter API key"),
            ("/logout", "Remove stored API key"),
            ("/history, /hist", "Show conversation history"),
            ("/clear", "Clear the screen"),
            ("/exit, /quit", "Exit the application")
        ]

        for cmd, desc in commands_help:
            help_table.add_row(cmd, desc)

        self.console.print(help_table)
        self.console.print("\n💡 You can also type questions directly to chat with the AI!", style="dim italic")

    async def show_models(self):
        """Show available models"""
        if not self.config_manager.get_api_key():
            self.console.print("❌ Please set your API key first with /login", style="red")
            return

        with self.console.status("[bold green]🔍 Fetching models from OpenRouter..."):
            self.available_models = await self.client.get_models()

        if not self.available_models:
            self.console.print("❌ Could not fetch models. Check your API key and internet connection.", style="red")
            return

        # Create models table with better formatting
        models_table = Table(title="🤖 Available AI Models", box=box.ROUNDED)
        models_table.add_column("Model ID", style="cyan", no_wrap=True)
        models_table.add_column("Name", style="white", max_width=40)
        models_table.add_column("Context", style="blue", justify="right")
        models_table.add_column("Current", style="green", justify="center")

        # Show popular models first, then others
        popular_models = []
        other_models = []

        for model in self.available_models:
            model_info = {
                "id": model["id"],
                "name": model.get("name", model["id"]),
                "context": model.get("context_length", "Unknown"),
                "is_current": model["id"] == self.client.current_model
            }

            # Categorize popular models
            if any(keyword in model["id"].lower() for keyword in ["gpt", "claude", "deepseek", "llama", "gemini"]):
                popular_models.append(model_info)
            else:
                other_models.append(model_info)

        # Display popular models first
        for model_info in popular_models[:15]:
            context_str = f"{model_info['context']:,}" if isinstance(model_info['context'], int) else str(model_info['context'])
            current_marker = "✅" if model_info['is_current'] else ""
            models_table.add_row(
                model_info['id'],
                model_info['name'],
                context_str,
                current_marker
            )

        self.console.print(models_table)

        # Show summary
        total_models = len(self.available_models)
        shown_models = min(15, len(popular_models))
        if total_models > shown_models:
            self.console.print(f"📊 Showing {shown_models} popular models out of {total_models} total", style="dim")
            self.console.print("💡 Use [bold yellow]/switch <model_id>[/bold yellow] to change models", style="dim")

    async def switch_model(self, model_id: str):
        """Switch to a different model"""
        if not model_id:
            self.console.print("❌ Please specify a model ID", style="red")
            return

        # Validate model exists if we have the list
        if self.available_models:
            valid_models = [m["id"] for m in self.available_models]
            if model_id not in valid_models:
                self.console.print(f"❌ Model '{model_id}' not found", style="red")
                return

        self.client.current_model = model_id
        self.config_manager.config["current_model"] = model_id
        self.config_manager.save_config()

        self.console.print(f"✅ Switched to model: {model_id}", style="green")

    async def login(self):
        """Set API key"""
        self.console.print("🔑 Enter your OpenRouter API key:")
        self.console.print("You can get one at: https://openrouter.ai/keys", style="dim")

        # Create a new session for password input
        session = PromptSession()
        try:
            api_key = await session.prompt_async("API Key: ", is_password=True)
            if api_key.strip():
                if self.config_manager.set_api_key(api_key.strip()):
                    self.client.api_key = api_key.strip()
                    self.console.print("✅ API key saved successfully!", style="green")
                else:
                    self.console.print("⚠️  API key set but could not be stored securely", style="yellow")
            else:
                self.console.print("❌ No API key provided", style="red")
        except (KeyboardInterrupt, EOFError):
            self.console.print("\n❌ Login cancelled", style="red")

    async def logout(self):
        """Remove API key"""
        try:
            keyring.delete_password("openrouter_cli", "api_key")
            self.client.api_key = None
            self.console.print("✅ API key removed", style="green")
        except:
            self.console.print("⚠️  Could not remove API key from secure storage", style="yellow")

    async def show_history(self):
        """Show conversation history"""
        if not self.history_manager.history:
            self.console.print("📚 No conversation history yet", style="dim")
            self.console.print("💡 Start a conversation by typing a question!", style="dim")
            return

        history_table = Table(title="📚 Conversation History", box=box.ROUNDED)
        history_table.add_column("#", style="dim", width=3)
        history_table.add_column("Time", style="cyan", no_wrap=True)
        history_table.add_column("User Message", style="white", max_width=50)
        history_table.add_column("Model", style="blue", no_wrap=True)
        history_table.add_column("Response Length", style="green", justify="right")

        # Show last 15 conversations
        recent_history = self.history_manager.history[-15:]
        for i, entry in enumerate(recent_history, 1):
            timestamp = datetime.fromisoformat(entry["timestamp"]).strftime("%m/%d %H:%M")
            user_preview = entry["user"][:47] + "..." if len(entry["user"]) > 50 else entry["user"]
            model_short = entry["model"].split('/')[-1] if '/' in entry["model"] else entry["model"]
            response_len = f"{len(entry['assistant'])} chars"

            history_table.add_row(
                str(len(self.history_manager.history) - len(recent_history) + i),
                timestamp,
                user_preview,
                model_short,
                response_len
            )

        self.console.print(history_table)

        total_entries = len(self.history_manager.history)
        if total_entries > 15:
            self.console.print(f"📊 Showing last 15 of {total_entries} total conversations", style="dim")

        # Show statistics
        if total_entries > 0:
            models_used = set(entry["model"] for entry in self.history_manager.history)
            self.console.print(f"🤖 Models used: {', '.join(sorted(models_used))}", style="dim")

    async def chat_with_ai(self, user_input: str):
        """Send message to AI and display response"""
        if not self.config_manager.get_api_key():
            self.console.print("❌ Please set your API key first with /login", style="red")
            return

        # Prepare messages for API with conversation context
        messages = []

        # Add recent context if available (last 2 conversations for context)
        if self.history_manager.history:
            recent = self.history_manager.history[-2:]
            for entry in recent:
                messages.append({"role": "user", "content": entry["user"]})
                messages.append({"role": "assistant", "content": entry["assistant"]})

        # Add current user message
        messages.append({"role": "user", "content": user_input})

        # Show thinking indicator with model name
        model_display = self.client.current_model.split('/')[-1] if '/' in self.client.current_model else self.client.current_model

        start_time = datetime.now()
        with self.console.status(f"[bold green]🤔 {model_display} is thinking..."):
            response = await self.client.chat_completion(messages)
        end_time = datetime.now()

        # Calculate response time
        response_time = (end_time - start_time).total_seconds()

        # Display response with metadata
        response_text = Text()
        response_text.append(response)

        # Add metadata footer
        response_text.append(f"\n\n", style="dim")
        response_text.append(f"⏱️ {response_time:.1f}s", style="dim")
        response_text.append(f" • 🔤 {len(response)} chars", style="dim")
        response_text.append(f" • 📝 {len(response.split())} words", style="dim")

        response_panel = Panel(
            response_text,
            title=f"🤖 {model_display}",
            subtitle=f"Response #{len(self.history_manager.history) + 1}",
            border_style="blue",
            box=box.ROUNDED
        )
        self.console.print(response_panel)

        # Save to history (only if response doesn't start with error emoji)
        if not response.startswith(("❌", "🔑", "💳", "🚦", "🌐", "⏱️")):
            self.history_manager.add_entry(user_input, response, self.client.current_model)
        else:
            # For errors, just show them without saving to history
            pass

    async def run(self):
        """Main application loop"""
        try:
            # Show welcome screen
            self.console.clear()
            self.console.print(self.create_welcome_panel())
            self.console.print()
            self.console.print(self.create_tips_panel())

            # Check API key status
            if not self.config_manager.get_api_key():
                self.console.print()
                self.console.print("⚠️  Missing API key • Run [bold yellow]/login[/bold yellow]", style="bold red")
            else:
                # Load current model from config
                saved_model = self.config_manager.config.get("current_model", "deepseek/deepseek-r1")
                self.client.current_model = saved_model
                self.console.print()
                self.console.print(f"✅ Ready! Current model: [bold blue]{saved_model}[/bold blue]", style="green")

            # Create prompt session with enhanced features
            self.session = PromptSession(
                completer=self.completer,
                key_bindings=self.bindings,
                complete_style="column",
                history=None,  # We manage our own history
                enable_history_search=True,
                mouse_support=True
            )

            # Main interaction loop
            while self.running:
                try:
                    self.console.print()

                    # Show current model in prompt if API key is set
                    if self.config_manager.get_api_key():
                        model_short = self.client.current_model.split('/')[-1] if '/' in self.client.current_model else self.client.current_model
                        prompt_text = f"<orange>You</orange> <dim>({model_short})</dim><orange> ></orange> "
                    else:
                        prompt_text = "<orange>You ></orange> "

                    user_input = await self.session.prompt_async(
                        HTML(prompt_text),
                        complete_style="column"
                    )

                    if not user_input.strip():
                        continue

                    # Check if it's a command
                    if user_input.startswith('/'):
                        await self.process_command(user_input)
                    else:
                        # Regular chat
                        await self.chat_with_ai(user_input)

                except (KeyboardInterrupt, EOFError):
                    self.console.print("\n👋 Goodbye! Thanks for using OpenRouter CLI!", style="cyan")
                    break
                except Exception as e:
                    self.console.print(f"❌ Unexpected error: {e}", style="red")
                    self.console.print("💡 Type /help for available commands", style="dim")

        except Exception as e:
            self.console.print(f"❌ Failed to start CLI: {e}", style="red")
            raise

if __name__ == "__main__":
    try:
        cli = OpenRouterCLI()
        asyncio.run(cli.run())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"Error: {e}")
